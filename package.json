{"name": "ai-caption-editor", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@clerk/backend": "^2.5.0", "@clerk/clerk-expo": "^2.14.9", "@clerk/expo-passkeys": "^0.3.20", "@expo-google-fonts/poppins": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@react-native-community/datetimepicker": "8.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@sentry/react-native": "^6.18.0", "convex": "^1.25.4", "expo": "~53.0.20", "expo-audio": "~0.4.8", "expo-blur": "~14.1.5", "expo-build": "^0.0.0", "expo-build-properties": "~0.14.8", "expo-checkbox": "~4.1.4", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-media-library": "~17.1.7", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-video": "~2.2.2", "expo-video-thumbnails": "~9.1.3", "expo-web-browser": "~14.2.0", "jotai": "^2.12.5", "nativewind": "^4.1.23", "picker": "^0.1.4", "properties": "^1.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-date-picker": "^5.0.13", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "prettier-plugin-tailwindcss": "^0.5.11", "typescript": "~5.8.3"}, "private": true}