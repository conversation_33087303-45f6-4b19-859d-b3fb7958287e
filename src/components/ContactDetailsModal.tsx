import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import React, { useEffect, useRef } from 'react';
import {
    Animated,
    Dimensions,
    Image,
    Modal,
    PanResponder,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { TagChip } from './TagChip';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const MODAL_HEIGHT = SCREEN_HEIGHT * 0.85;

interface ContactDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  contact: {
    id: string;
    name: string;
    company: string;
    lastContact: string;
    avatarUrl?: string;
    email?: string;
    phone?: string;
    jobTitle?: string;
    location?: string;
    tags?: string[];
  } | null;
}

export default function ContactDetailsModal({ visible, onClose, contact }: ContactDetailsModalProps) {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  const allTags = useQuery(api.tags.getTags);
  const contactTags = allTags?.filter(tag => contact?.tags?.includes(tag._id)) || [];
  const slideAnim = useRef(new Animated.Value(MODAL_HEIGHT)).current;
  const panY = useRef(new Animated.Value(0)).current;

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#000000',
    modalBackground: isDarkMode ? '#1C1C1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#A0A0A0' : '#666666',
    border: isDarkMode ? '#3A3A3A' : '#E0E0E0',
    iconBackground: isDarkMode ? '#2A2A2A' : '#F0F0F0',
    buttonBackground: isDarkMode ? '#007AFF' : '#007AFF',
    sectionBackground: isDarkMode ? '#2A2A2A' : '#F8F9FA',
  };

  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.spring(slideAnim, {
        toValue: MODAL_HEIGHT,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dy) > 5;
      },
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dy > 0) {
          panY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          onClose();
        } else {
          Animated.spring(panY, {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }
      },
    })
  ).current;

  const logNewInteraction = () => {
    console.log('Log New Interaction button pressed');
  };

  if (!contact) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={[styles.overlay, { backgroundColor: colors.background + '80' }]}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              backgroundColor: colors.modalBackground,
              transform: [
                { translateY: slideAnim },
                { translateY: panY },
              ],
            },
          ]}
          {...panResponder.panHandlers}
        >
            {/* Handle bar */}
            <View style={styles.handleContainer}>
              <View style={[styles.handle, { backgroundColor: colors.secondaryText }]} />
            </View>

            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity onPress={onClose} style={styles.backButton}>
                <Feather name="arrow-left" size={24} color={colors.text} />
              </TouchableOpacity>
              <Text style={[styles.headerTitle, { color: colors.text }]}>Contact Details</Text>
              <View style={{ width: 24 }} />
            </View>

            <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
              {/* Profile Section */}
              <View style={styles.profileSection}>
                <Image
                  source={{ uri: contact.avatarUrl || 'https://via.placeholder.com/120' }}
                  style={styles.profileImage}
                />
                <Text style={[styles.contactName, { color: colors.text }]}>{contact.name}</Text>
                <Text style={[styles.jobTitle, { color: colors.secondaryText }]}>
                  {contact.jobTitle || 'Product Manager'} at {contact.company || 'TechCorp'}
                </Text>
                <Text style={[styles.lastContact, { color: colors.secondaryText }]}>
                  Last contacted: {contact.lastContact}
                </Text>

                {/* Tags */}
                {contactTags.length > 0 && (
                  <View style={styles.tagsContainer}>
                    {contactTags.map((tag) => (
                      <TagChip
                        key={tag._id}
                        tag={tag}
                        size="medium"
                      />
                    ))}
                  </View>
                )}
              </View>

              {/* Contact Information */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Contact Information</Text>
                
                <View style={[styles.contactInfoItem, { backgroundColor: colors.sectionBackground }]}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                    <Feather name="mail" size={20} color={colors.text} />
                  </View>
                  <View style={styles.contactInfoText}>
                    <Text style={[styles.contactInfoLabel, { color: colors.text }]}>Email</Text>
                    <Text style={[styles.contactInfoValue, { color: colors.secondaryText }]}>
                      {contact.email || '<EMAIL>'}
                    </Text>
                  </View>
                </View>

                <View style={[styles.contactInfoItem, { backgroundColor: colors.sectionBackground }]}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                    <Feather name="phone" size={20} color={colors.text} />
                  </View>
                  <View style={styles.contactInfoText}>
                    <Text style={[styles.contactInfoLabel, { color: colors.text }]}>Phone</Text>
                    <Text style={[styles.contactInfoValue, { color: colors.secondaryText }]}>
                      {contact.phone || '+****************'}
                    </Text>
                  </View>
                </View>

                <View style={[styles.contactInfoItem, { backgroundColor: colors.sectionBackground }]}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                    <Feather name="linkedin" size={20} color={colors.text} />
                  </View>
                  <View style={styles.contactInfoText}>
                    <Text style={[styles.contactInfoLabel, { color: colors.text }]}>LinkedIn</Text>
                    <Text style={[styles.contactInfoValue, { color: colors.secondaryText }]}>
                      LinkedIn Profile
                    </Text>
                  </View>
                </View>
              </View>

              {/* Interaction History */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Interaction History</Text>
                
                <View style={[styles.interactionItem, { backgroundColor: colors.sectionBackground }]}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                    <Feather name="calendar" size={20} color={colors.text} />
                  </View>
                  <View style={styles.interactionText}>
                    <Text style={[styles.interactionTitle, { color: colors.text }]}>Initial Meeting</Text>
                    <Text style={[styles.interactionDescription, { color: colors.secondaryText }]}>
                      Met at industry conference
                    </Text>
                  </View>
                </View>

                <View style={[styles.interactionItem, { backgroundColor: colors.sectionBackground }]}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                    <Feather name="phone" size={20} color={colors.text} />
                  </View>
                  <View style={styles.interactionText}>
                    <Text style={[styles.interactionTitle, { color: colors.text }]}>Follow-up Call</Text>
                    <Text style={[styles.interactionDescription, { color: colors.secondaryText }]}>
                      Discussed potential collaboration
                    </Text>
                  </View>
                </View>
              </View>

              {/* Notes */}
              <View style={styles.section}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Notes</Text>
                <View style={[styles.notesContainer, { backgroundColor: colors.sectionBackground }]}>
                  <Text style={[styles.notesText, { color: colors.text }]}>
                    Ethan is a key contact for potential partnerships. He&apos;s interested in exploring new
                    technologies and has a strong network in the tech industry.
                  </Text>
                </View>
              </View>

              {/* Log New Interaction Button */}
              <TouchableOpacity
                style={[styles.logButton, { backgroundColor: colors.buttonBackground }]}
                onPress={logNewInteraction}
              >
                <Feather name="plus" size={20} color="white" />
                <Text style={styles.logButtonText}>Log New Interaction</Text>
              </TouchableOpacity>

              <View style={{ height: 40 }} />
            </ScrollView>
          </Animated.View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  modalContainer: {
    height: MODAL_HEIGHT,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handleContainer: {
    alignItems: 'center',
    paddingTop: 8,
    paddingBottom: 8,
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  contactName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  jobTitle: {
    fontSize: 16,
    marginBottom: 8,
  },
  lastContact: {
    fontSize: 14,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  contactInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  contactInfoText: {
    flex: 1,
  },
  contactInfoLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  contactInfoValue: {
    fontSize: 14,
  },
  interactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  interactionText: {
    flex: 1,
  },
  interactionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  interactionDescription: {
    fontSize: 14,
  },
  notesContainer: {
    padding: 16,
    borderRadius: 12,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  logButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  logButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 16,
    justifyContent: 'center',
  },
});
