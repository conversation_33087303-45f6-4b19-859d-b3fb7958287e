// Type definitions for the application

export type FeatherIconName = 
  | 'phone'
  | 'mail'
  | 'users'
  | 'message-square'
  | 'share-2'
  | 'more-horizontal'
  | 'x';

export type InteractionType = 'call' | 'email' | 'meeting' | 'text' | 'social' | 'other';

export interface ContactHistoryFormData {
  title: string;
  notes: string;
  interactionType: InteractionType;
  location: string;
  duration: number;
  followUpRequired: boolean;
  followUpDate: string;
}
