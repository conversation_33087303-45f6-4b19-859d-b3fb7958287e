import { TagInput } from "@/components/TagInput";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { useMutation, useQuery } from "convex/react";
import * as ImagePicker from 'expo-image-picker';
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Image,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
  useColorScheme,
} from "react-native";

interface ContactHistory {
  id: string;
  type: 'call' | 'email';
  date: string;
  description: string;
}

export default function EditContact() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  
  const contact = useQuery(api.contacts.getContactById, id ? { id } : 'skip');
  const updateContact = useMutation(api.contacts.updateContact);
  const generatedUploadUrl = useMutation(api.contacts.generatedUploadUrl);
  const avatarUrlFromId = useMutation(api.contacts.avatarUrlFromId);
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    jobTitle: '',
    company: '',
    location: '',
    linkedin: '',
    notes: '',
  });

  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [avatar, setAvatar] = useState<string | null>(null);
  const [existingAvatar, setExistingAvatar] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const styles = createStyles(isDark);

  // Initialize form with contact data when it's loaded
  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name || '',
        email: contact.email || '',
        phone: contact.phone || '',
        jobTitle: contact.jobTitle || '',
        company: contact.company || '',
        location: contact.location || '',
        linkedin: contact.linkedin || '',
        notes: contact.notes || '',
      });
      setSelectedTags(contact.tags || []);
      if (contact.avatarUrl) {
        setExistingAvatar(contact.avatarUrl);
      }
      setIsLoading(false);
    }
  }, [contact]);
  
  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });
    
    if (!result.canceled) {
      setAvatar(result.assets[0].uri);
      setExistingAvatar(null); // Clear existing avatar when a new one is selected
    }
  };

  const handleAvatarUpload = async () => {
    if (avatar) {
      const uploadUrl = await generatedUploadUrl();
      const blob = await fetch(avatar).then((res) => res.blob());
      const response = await fetch(uploadUrl, {
        method: 'POST',
        headers: { 'Content-Type': blob!.type },
        body: blob,
      });
      if (!response.ok) {
        throw new Error('Failed to upload avatar');
      }
      const { storageId } = await response.json();
      return storageId;
    }
    return null;
  };
  
  const handleUpdateContact = async () => {
    try {
      if (!formData.name.trim()) {
        Alert.alert('Error', 'Name is required');
        return;
      }

      let storageId = null;
      if (avatar) {
        storageId = await handleAvatarUpload();
      }
      
      // Prepare the update data
      const updateData: any = {
        id: id as Id<'contacts'>,
        name: formData.name,
        phone: formData.phone,
        ...(formData.email && { email: formData.email }),
        ...(formData.jobTitle && { jobTitle: formData.jobTitle }),
        ...(formData.company && { company: formData.company }),
        ...(formData.location && { location: formData.location }),
        ...(formData.linkedin && { linkedin: formData.linkedin }),
        ...(formData.notes && { notes: formData.notes }),
        tags: selectedTags as Id<"tags">[],
      };
      
      if (storageId) {
        updateData.avatar = storageId as Id<"_storage">;
        try {
          const avatarUrl = await avatarUrlFromId({ id: storageId });
          if (avatarUrl) {
            updateData.avatarUrl = avatarUrl;
          }
        } catch (error) {
          console.error('Error getting avatar URL:', error);
        }
      }
      
      await updateContact(updateData);
      
      Alert.alert('Success', 'Contact updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update contact');
      console.error(error);
    }
  };
  
  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  if (isLoading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: isDark ? '#fff' : '#000' }}>Loading contact...</Text>
      </View>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Pressable onPress={() => router.back()} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </Pressable>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <Pressable 
            onPress={handleUpdateContact} 
            style={styles.saveButton}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </Pressable>
        </View>
        
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <Pressable onPress={pickImage} style={styles.avatarContainer}>
            {avatar ? (
              <Image source={{ uri: avatar }} style={styles.avatar} />
            ) : existingAvatar ? (
              <Image source={{ uri: existingAvatar }} style={styles.avatar} />
            ) : (
              <View style={styles.defaultAvatar}>
                <Text style={styles.avatarText}>
                  {formData.name ? formData.name.charAt(0).toUpperCase() : '?'}
                </Text>
              </View>
            )}
            <View style={styles.editBadge}>
              <Text style={[styles.editBadgeText, { fontSize: 20, marginBottom: 5 }]}>+</Text>
            </View>
          </Pressable>
        </View>
        
        {/* Form Fields */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter full name"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.name}
              onChangeText={(value) => updateFormData('name', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter email address"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.email}
              onChangeText={(value) => updateFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Phone</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter phone number"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.phone}
              onChangeText={(value) => updateFormData('phone', value)}
              keyboardType="phone-pad"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Job Title</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter job title"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.jobTitle}
              onChangeText={(value) => updateFormData('jobTitle', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Company</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter company name"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.company}
              onChangeText={(value) => updateFormData('company', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter location"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.location}
              onChangeText={(value) => updateFormData('location', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>LinkedIn</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter LinkedIn profile URL"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.linkedin}
              onChangeText={(value) => updateFormData('linkedin', value)}
              autoCapitalize="none"
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Add notes about this contact"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <TagInput
              selectedTags={selectedTags}
              onTagsChange={setSelectedTags}
              placeholder="Add tags..."
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? '#121212' : '#fff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? '#2A2A2A' : '#E0E0E0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_600SemiBold',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 24,
    color: isDark ? '#fff' : '#000',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: isDark ? '#4A90E2' : '#2563EB',
    fontFamily: 'Poppins_600SemiBold',
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  avatarContainer: {
    position: 'relative',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
  },
  defaultAvatar: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 48,
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_600SemiBold',
  },
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: isDark ? '#4A90E2' : '#2563EB',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: isDark ? '#121212' : '#fff',
  },
  editBadgeText: {
    color: '#fff',
    fontSize: 24,
    lineHeight: 24,
    marginTop: -2,
  },
  formSection: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: isDark ? '#A0A0A0' : '#666',
    marginBottom: 6,
    fontFamily: 'Poppins_500Medium',
  },
  input: {
    backgroundColor: isDark ? '#2A2A2A' : '#F5F5F5',
    borderRadius: 10,
    padding: 14,
    fontSize: 16,
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_400Regular',
  },
  notesInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
});
