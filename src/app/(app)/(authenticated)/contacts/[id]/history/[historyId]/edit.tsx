import { TagInput } from "@/components/TagInput";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import DatePicker from '@react-native-community/datetimepicker';
import { useMutation, useQuery } from "convex/react";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  View,
  useColorScheme
} from "react-native";

interface ContactHistoryFormData {
  title: string;
  notes: string;
  interactionType: 'call' | 'email' | 'meeting' | 'text' | 'social' | 'other';
  location: string;
  duration: number;
  followUpRequired: boolean;
  followUpDate: Date | null;
  itemsDiscussed: string[] | undefined;
}

export default function EditContactHistory() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [ isLoading, setIsLoading ] = useState<boolean>(false);
  
  const interaction = useQuery(api.contactHistory.getContactHistoryById, id ? { id } : 'skip');
  const updateContactHistory = useMutation(api.contactHistory.updateContactHistory);
  
  const [formData, setFormData] = useState<ContactHistoryFormData>({
    title: '',
    notes: '',
    interactionType: 'call',
    location: '',
    duration: 0,
    followUpRequired: false,
    followUpDate: new Date(),
    itemsDiscussed: [],
  });

  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const styles = createStyles(isDark);

  // Initialize form with contact data when it's loaded
  useEffect(() => {
    if (interaction) {
      const { title, notes, interactionType, tags, itemsDiscussed, location, duration, followUpRequired, followUpDate } = interaction;
      switch (interactionType) {
        case 'call': setFormData({ ...formData, interactionType: 'call' }); break;
        case 'email': setFormData({ ...formData, interactionType: 'email' }); break;
        case 'meeting': setFormData({ ...formData, interactionType: 'meeting' }); break;
        case 'text': setFormData({ ...formData, interactionType: 'text' }); break;
        case 'social': setFormData({ ...formData, interactionType: 'social' }); break;
        case 'other': setFormData({ ...formData, interactionType: 'other' }); break;
        default: setFormData({ ...formData, interactionType: 'call' });
      }
      if (followUpDate) {
        setFormData({ ...formData, followUpDate: new Date(followUpDate) });
      }
      if (itemsDiscussed) {
        setFormData({ ...formData, itemsDiscussed: itemsDiscussed });
      }
      if (title) {
        setFormData({ ...formData, title: title });
      }
      if (notes) {
        setFormData({ ...formData, notes: notes });
      }
      if (location) {
        setFormData({ ...formData, location: location });
      }
      if (duration) {
        setFormData({ ...formData, duration: duration });
      }
      if (followUpRequired) {
        setFormData({ ...formData, followUpRequired: followUpRequired });
      }
      setSelectedTags(interaction?.tags || []);
      setIsLoading(false);
    }
  }, [interaction]);
  
  const handleUpdateContactHistory = async () => {
    try {
      if (!formData.title.trim()) {
        Alert.alert('Error', 'Title is required');
        return;
      }
      
      // Prepare the update data
      const updateData: any = {
        id: id as Id<'contacts'>,
        title: formData.title,
        notes: formData.notes,
        interactionType: formData.interactionType,
        location: formData.location,
        duration: formData.duration,
        followUpRequired: formData.followUpRequired,
        followUpDate: formData.followUpDate,
        tags: selectedTags,
        itemsDiscussed: formData.itemsDiscussed,
      };
      
      await updateContactHistory(updateData);
      
      Alert.alert('Success', 'Contact updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update contact');
      console.error(error);
    }
  };
  
  // Update updateFormData to handle different types
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  if (isLoading) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: isDark ? '#fff' : '#000' }}>Loading contact...</Text>
      </View>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Pressable onPress={() => router.back()} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </Pressable>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <Pressable 
            onPress={handleUpdateContactHistory} 
            style={styles.saveButton}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </Pressable>
        </View>
        
        {/* Form Fields */}
        <View style={styles.formSection}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter title"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.title}
              onChangeText={(value) => updateFormData('title', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter notes"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.notes}
              onChangeText={(value) => updateFormData('notes', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Interaction Type</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter interaction type"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.interactionType}
              onChangeText={(value) => updateFormData('interactionType', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter location"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.location}
              onChangeText={(value) => updateFormData('location', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Duration</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter duration"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.duration.toString()}
              onChangeText={(value) => updateFormData('duration', value)}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Follow Up Required</Text>
            <Switch
              value={Boolean(formData.followUpRequired)}
              onValueChange={(value) => updateFormData('followUpRequired', value)}
            />
          </View>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Follow Up Date</Text>
            <DatePicker
              value={
                formData.followUpDate instanceof Date
                  ? formData.followUpDate
                  : new Date()
              }
              onChange={(event, date) => {
                if (date) {
                  updateFormData('followUpDate', date);
                }
              }}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <TagInput
              selectedTags={selectedTags}
              onTagsChange={setSelectedTags}
              placeholder="Add tags..."
            />
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? '#121212' : '#fff',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: isDark ? '#2A2A2A' : '#E0E0E0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_600SemiBold',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 24,
    color: isDark ? '#fff' : '#000',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: isDark ? '#4A90E2' : '#2563EB',
    fontFamily: 'Poppins_600SemiBold',
  },
  avatarSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  avatarContainer: {
    position: 'relative',
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: isDark ? '#2A2A2A' : '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
  },
  defaultAvatar: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 48,
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_600SemiBold',
  },
  editBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: isDark ? '#4A90E2' : '#2563EB',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: isDark ? '#121212' : '#fff',
  },
  editBadgeText: {
    color: '#fff',
    fontSize: 24,
    lineHeight: 24,
    marginTop: -2,
  },
  formSection: {
    paddingHorizontal: 16,
    paddingBottom: 32,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: isDark ? '#A0A0A0' : '#666',
    marginBottom: 6,
    fontFamily: 'Poppins_500Medium',
  },
  input: {
    backgroundColor: isDark ? '#2A2A2A' : '#F5F5F5',
    borderRadius: 10,
    padding: 14,
    fontSize: 16,
    color: isDark ? '#fff' : '#000',
    fontFamily: 'Poppins_400Regular',
  },
  notesInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
});
