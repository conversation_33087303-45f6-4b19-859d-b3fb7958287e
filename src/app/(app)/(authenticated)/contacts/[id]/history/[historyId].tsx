import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useRef } from 'react';
import {
  Dimensions,
  PanResponder,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const getInteractionIcon = (type: string) => {
  switch (type) {
    case 'call': return 'phone';
    case 'email': return 'mail';
    case 'meeting': return 'users';
    case 'text': return 'message-square';
    case 'social': return 'share-2';
    default: return 'more-horizontal';
  }
};

const getInteractionColor = (type: string) => {
  switch (type) {
    case 'call': return '#34C759';
    case 'email': return '#007AFF';
    case 'meeting': return '#FF9500';
    case 'text': return '#5856D6';
    case 'social': return '#FF2D92';
    default: return '#8E8E93';
  }
};

export default function ContactHistoryDetailPage() {
  const { theme } = useTheme();
  const router = useRouter();
  const { id, historyId } = useLocalSearchParams<{ id: string; historyId: string }>();
  const isDarkMode = theme === 'dark';

  const history = useQuery(api.contactHistory.getContactHistoryById, historyId ? { id: historyId } : 'skip');
  const contact = useQuery(api.contacts.getContactById, id ? { id } : 'skip');

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    pageBackground: isDarkMode ? '#1C1C1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#A0A0A0' : '#666666',
    border: isDarkMode ? '#3A3A3A' : '#E0E0E0',
    iconBackground: isDarkMode ? '#2A2A2A' : '#F0F0F0',
    buttonBackground: isDarkMode ? '#007AFF' : '#007AFF',
    sectionBackground: isDarkMode ? '#2A2A2A' : '#F8F9FA',
    chipBackground: isDarkMode ? '#333' : '#f0f0f0',
    chipText: isDarkMode ? '#fff' : '#000',
  };

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return Math.abs(gestureState.dx) > 5;
      },
      onPanResponderRelease: (evt, gestureState) => {
        if (gestureState.dx > 50 || gestureState.vx > 0.3) {
          router.back();
        }
      },
    })
  ).current;

  const handleClose = () => {
    router.back();
  };

  const handleEdit = () => {
    // Pass a parameter in the URL to tell the edit screen not to animate.
    router.push(`/(app)/(authenticated)/contacts/${id}/history/${historyId}/edit?animation=none`);
  };

  if (!history || !contact) {
    return (
      <View style={[styles.container, { backgroundColor: colors.pageBackground }]}>
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading interaction...</Text>
      </View>
    );
  }

  const interactionColor = getInteractionColor(history.interactionType);

  return (
    <View style={[styles.container, { backgroundColor: colors.pageBackground }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
          <Feather name="chevron-left" size={28} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Interaction Details</Text>
        <TouchableOpacity onPress={handleEdit} style={styles.editButton}>
          <Feather name="edit-2" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Header Section */}
        <View style={[styles.headerSection, { backgroundColor: colors.sectionBackground }]}>
          <View style={[styles.iconContainer, { backgroundColor: interactionColor }]}>
            <Feather name={getInteractionIcon(history.interactionType)} size={24} color="white" />
          </View>
          
          <Text style={[styles.title, { color: colors.text }]}>
            {history.title}
          </Text>
          
          <Text style={[styles.subtitle, { color: colors.secondaryText }]}>
            {new Date(history.dateTime).toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })} at {new Date(history.dateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>

        {/* Details Section */}
        <View style={styles.detailsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Details</Text>
          
          <View style={[styles.detailRow, { borderBottomColor: colors.border }]}>
            <View style={[styles.detailIconContainer, { backgroundColor: colors.iconBackground }]}>
              <Feather name="tag" size={20} color={colors.text} />
            </View>
            <View style={styles.detailContent}>
              <Text style={[styles.detailLabel, { color: colors.secondaryText }]}>Type</Text>
              <Text style={[styles.detailValue, { color: colors.text }]}>
                {history.interactionType.charAt(0).toUpperCase() + history.interactionType.slice(1)}
              </Text>
            </View>
          </View>

          {history.duration && (
            <View style={[styles.detailRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.detailIconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="clock" size={20} color={colors.text} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: colors.secondaryText }]}>Duration</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {history.duration} minutes
                </Text>
              </View>
            </View>
          )}

          {history.location && (
            <View style={[styles.detailRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.detailIconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="map-pin" size={20} color={colors.text} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: colors.secondaryText }]}>Location</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {history.location}
                </Text>
              </View>
            </View>
          )}

          {history.followUpRequired && (
            <View style={[styles.detailRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.detailIconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="calendar" size={20} color={colors.text} />
              </View>
              <View style={styles.detailContent}>
                <Text style={[styles.detailLabel, { color: colors.secondaryText }]}>Follow-up</Text>
                <Text style={[styles.detailValue, { color: colors.text }]}>
                  {history.followUpDate 
                    ? `Required by ${new Date(history.followUpDate).toLocaleDateString()}`
                    : 'Required'
                  }
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Tags Section */}
        {history.tags && history.tags.length > 0 && (
          <View style={styles.tagsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Tags</Text>
            <View style={styles.tagsContainer}>
              {history.tags.map((tag, index) => (
                <View key={index} style={[styles.tag, { backgroundColor: colors.chipBackground }]}>
                  <Text style={[styles.tagText, { color: colors.chipText }]}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Items Discussed Section */}
        {history.itemsDiscussed && history.itemsDiscussed.length > 0 && (
          <View style={styles.itemsSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Items Discussed</Text>
            {history.itemsDiscussed.map((item, index) => (
              <View key={index} style={[styles.itemRow, { borderBottomColor: colors.border }]}>
                <View style={[styles.bulletPoint, { backgroundColor: colors.text }]} />
                <Text style={[styles.itemText, { color: colors.text }]}>{item}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Notes Section */}
        {history.notes && (
          <View style={styles.notesSection}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Notes</Text>
            <View style={[styles.notesContainer, { backgroundColor: colors.sectionBackground }]}>
              <Text style={[styles.notesText, { color: colors.text }]}>{history.notes}</Text>
            </View>
          </View>
        )}

        <View style={{ height: 40 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  editButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  headerSection: {
    paddingVertical: 32,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  detailsSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  detailIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
  },
  tagsSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '500',
  },
  itemsSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  itemRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 8,
    marginRight: 12,
  },
  itemText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 22,
  },
  notesSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  notesContainer: {
    padding: 16,
    borderRadius: 12,
  },
  notesText: {
    fontSize: 16,
    lineHeight: 22,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
});