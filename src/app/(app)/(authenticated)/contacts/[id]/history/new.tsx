// new interaction page
import { api } from '@/convex/_generated/api';
import type { ContactHistoryFormData, FeatherIconName, InteractionType } from '@/types';
import { Feather } from '@expo/vector-icons';
import { useMutation } from 'convex/react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
  Alert,
  Animated,
  Dimensions,
  PanResponder,
  Pressable,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  useColorScheme,
} from 'react-native';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

interface InteractionTypeOption {
  value: InteractionType;
  label: string;
  icon: FeatherIconName;
}

const INTERACTION_TYPES: InteractionTypeOption[] = [
  { value: 'call', label: 'Phone Call', icon: 'phone' },
  { value: 'email', label: 'Email', icon: 'mail' },
  { value: 'meeting', label: 'Meeting', icon: 'users' },
  { value: 'text', label: 'Text/SMS', icon: 'message-square' },
  { value: 'social', label: 'Social Media', icon: 'share-2' },
  { value: 'other', label: 'Other', icon: 'more-horizontal' },
];

export default function NewContactHistory() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const router = useRouter();
  const params = useLocalSearchParams<{ id: string }>();
  const id = params.id as string;

  const styles = createStyles(isDark);
  
  const createContactHistory = useMutation(api.contactHistory.createContactHistory);
  
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  
  const [formData, setFormData] = useState<ContactHistoryFormData>({
    title: '',
    notes: '',
    interactionType: 'call',
    location: '',
    duration: 0,
    followUpRequired: false,
    followUpDate: new Date().toISOString().split('T')[0],
  });
  
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [itemsDiscussed, setItemsDiscussed] = useState<string[]>([]);
  const [newItem, setNewItem] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 80,
      friction: 8,
    }).start();
  }, []);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        return gestureState.dy > 5;
      },
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.dy > 0) {
          slideAnim.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (evt, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.3) {
          handleClose();
        } else {
          Animated.spring(slideAnim, {
            toValue: 0,
            useNativeDriver: true,
          }).start();
        }
      },
    })
  ).current;

  const handleClose = () => {
    Animated.timing(slideAnim, {
      toValue: SCREEN_HEIGHT,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      router.back();
    });
  };

  const handleCreateHistory = async () => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Please enter a title for this interaction.');
      return;
    }

    if (formData.duration < 0) {
      Alert.alert('Error', 'Duration cannot be negative.');
      return;
    }

    try {
      await createContactHistory({
        contactId: id!,
        title: formData.title,
        notes: formData.notes,
        interactionType: formData.interactionType,
        location: formData.location,
        duration: formData.duration,
        followUpRequired: formData.followUpRequired,
        followUpDate: parseInt(formData.followUpDate),
        tags,
        itemsDiscussed,
        dateTime: selectedDate.getTime(),
      });

      router.back();
    } catch (error) {
      console.error('Error creating history:', error);
      Alert.alert('Error', 'Failed to create history entry. Please try again.');
    }
  };

  const updateFormData = <K extends keyof ContactHistoryFormData>(
    field: K, 
    value: ContactHistoryFormData[K]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addTag = () => {
    if (newTag.trim()) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const addItem = () => {
    if (newItem.trim()) {
      setItemsDiscussed([...itemsDiscussed, newItem.trim()]);
      setNewItem('');
    }
  };

  const removeItem = (index: number) => {
    setItemsDiscussed(itemsDiscussed.filter((_, i) => i !== index));
  };

  const animatedStyle = {
    transform: [{ translateY: slideAnim }],
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Pressable onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </Pressable>
          <Text style={styles.headerTitle}>Log Interaction</Text>
          <Pressable onPress={handleCreateHistory} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save</Text>
          </Pressable>
        </View>

        {/* Form Fields */}
        <View style={styles.formSection}>
          {/* Title */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Title *</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter interaction title"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.title}
              onChangeText={(text) => setFormData({ ...formData, title: text })}
            />
          </View>

          {/* Interaction Type */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Interaction Type</Text>
            <View style={styles.typeGrid}>
              {INTERACTION_TYPES.map((type) => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.typeButton,
                    formData.interactionType === type.value && styles.typeButtonActive,
                  ]}
                  onPress={() => setFormData({ ...formData, interactionType: type.value })}
                >
                  <Feather 
                    name={type.icon} 
                    size={16} 
                    color={formData.interactionType === type.value ? '#fff' : (isDark ? '#fff' : '#000')} 
                  />
                  <Text style={[
                    styles.typeButtonText,
                    formData.interactionType === type.value && styles.typeButtonTextActive,
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Date & Time */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Follow-up Date</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={[styles.dateText, { color: formData.followUpDate ? (isDark ? '#fff' : '#000') : (isDark ? '#666' : '#999') }]}>
                {formData.followUpDate || 'Select follow-up date'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Duration */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Duration (minutes)</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., 30"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.duration.toString()}
              onChangeText={(text) => {
                const num = parseInt(text, 10);
                setFormData({ ...formData, duration: isNaN(num) ? 0 : num });
              }}
              keyboardType="number-pad"
            />
          </View>

          {/* Location */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              placeholder="Where did this interaction take place?"
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.location}
              onChangeText={(text) => setFormData({ ...formData, location: text })}
            />
          </View>

          {/* Tags */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Tags</Text>
            <View style={styles.chipContainer}>
              {tags.map((tag, index) => (
                <View
                  key={index}
                  style={styles.chip}
                >
                  <Text style={styles.chipText}>{tag}</Text>
                  <TouchableOpacity
                    style={styles.chipRemove}
                    onPress={() => removeTag(index)}
                  >
                    <Feather name="x" size={12} color={isDark ? '#000' : '#fff'} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            <View style={styles.addInputContainer}>
              <TextInput
                style={styles.addInput}
                placeholder="Add a tag"
                placeholderTextColor={isDark ? '#666' : '#999'}
                value={newTag}
                onChangeText={setNewTag}
                onSubmitEditing={addTag}
              />
              <TouchableOpacity style={styles.addButton} onPress={addTag}>
                <Feather name="plus" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Items Discussed */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Items Discussed</Text>
            <View style={styles.chipContainer}>
              {itemsDiscussed.map((item, index) => (
                <View
                  key={index}
                  style={styles.chip}
                >
                  <Text style={styles.chipText}>{item}</Text>
                  <TouchableOpacity
                    style={styles.chipRemove}
                    onPress={() => removeItem(index)}
                  >
                    <Feather name="x" size={12} color={isDark ? '#000' : '#fff'} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            <View style={styles.addInputContainer}>
              <TextInput
                style={styles.addInput}
                placeholder="Add discussion topic"
                placeholderTextColor={isDark ? '#666' : '#999'}
                value={newItem}
                onChangeText={setNewItem}
                onSubmitEditing={addItem}
              />
              <TouchableOpacity style={styles.addButton} onPress={addItem}>
                <Feather name="plus" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Notes */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes</Text>
            <TextInput
              style={[styles.input, styles.notesInput]}
              placeholder="Add detailed notes about the interaction..."
              placeholderTextColor={isDark ? '#666' : '#999'}
              value={formData.notes}
              onChangeText={(text) => setFormData({ ...formData, notes: text })}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          {/* Follow-up */}
          <View style={styles.inputGroup}>
            <TouchableOpacity
              style={styles.checkboxContainer}
              onPress={() => setFormData({ ...formData, followUpRequired: !formData.followUpRequired })}
            >
              <TouchableOpacity
                style={[
                  styles.checkbox,
                  formData.followUpRequired && styles.checkboxChecked,
                ]}
                onPress={() => setFormData({ ...formData, followUpRequired: !formData.followUpRequired })}
              >
                {formData.followUpRequired && <Feather name="check" size={12} color="#fff" />}
              </TouchableOpacity>
              <Text style={styles.checkboxLabel}>Follow-up required</Text>
            </TouchableOpacity>

            {formData.followUpRequired && (
              <TextInput
                style={styles.input}
                placeholder="Follow-up date (YYYY-MM-DD)"
                placeholderTextColor={isDark ? '#666' : '#999'}
                value={formData.followUpDate}
                onChangeText={(text) => setFormData({ ...formData, followUpDate: text })}
              />
            )}
          </View>
        </View>

        {/* Create Button */}
        <Pressable onPress={handleCreateHistory} style={styles.createButton}>
          <Text style={styles.createButtonText}>Save Interaction</Text>
        </Pressable>

        <View style={{ height: 40 }} />
      </ScrollView>
    </SafeAreaView>
  );
}

function createStyles(isDark: boolean) {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? '#121212' : '#fff',
    },
    saveButton: {
      position: 'absolute',
      right: 0,
      padding: 8,
    },
    saveButtonText: {
      fontSize: 16,
      color: '#007AFF',
      fontWeight: '600',
    },
    typeGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    typeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 12,
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
      marginBottom: 8,
    },
    typeButtonActive: {
      backgroundColor: '#007AFF',
      borderColor: '#007AFF',
    },
    typeButtonText: {
      marginLeft: 8,
      fontSize: 15,
      fontWeight: '500',
      color: isDark ? '#fff' : '#000',
    },
    typeButtonTextActive: {
      color: '#fff',
    },
    dateText: {
      fontSize: 16,
    },
    chipContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 8,
    },
    chip: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: '#007AFF',
    },
    chipText: {
      fontSize: 14,
      color: '#fff',
      marginRight: 4,
    },
    chipRemove: {
      marginLeft: 4,
      padding: 2,
    },
    addInputContainer: {
      flexDirection: 'row',
      gap: 8,
    },
    addInput: {
      flex: 1,
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
    },
    addButton: {
      width: 48,
      height: 48,
      borderRadius: 12,
      backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    checkbox: {
      width: 22,
      height: 22,
      borderRadius: 6,
      borderWidth: 2,
      borderColor: isDark ? '#333' : '#e0e0e0',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 12,
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
    },
    checkboxChecked: {
      backgroundColor: '#007AFF',
      borderColor: '#007AFF',
    },
    checkboxLabel: {
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
      fontWeight: '500',
    },
    scrollView: {
      flex: 1,
      paddingHorizontal: 20,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 20,
      position: 'relative',
    },
    closeButton: {
      position: 'absolute',
      left: 0,
      padding: 8,
    },
    closeButtonText: {
      fontSize: 18,
      color: isDark ? '#fff' : '#000',
      fontWeight: '300',
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: isDark ? '#fff' : '#000',
    },
    avatarSection: {
      alignItems: 'center',
      marginBottom: 30,
    },
    avatarContainer: {
      position: 'relative',
    },
    avatar: {
      width: 120,
      height: 120,
      borderRadius: 60,
    },
    defaultAvatar: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: isDark ? '#333' : '#f0f0f0',
      justifyContent: 'center',
      alignItems: 'center',
    },
    editBadge: {
      position: 'absolute',
      bottom: 0,
      right: 0,
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: isDark ? '#000' : '#fff',
    },
    editBadgeText: {
      color: '#fff',
      fontSize: 16,
      fontWeight: 'bold',
    },
    formSection: {
      marginBottom: 30,
    },
    inputGroup: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: '500',
      color: isDark ? '#fff' : '#000',
      marginBottom: 8,
    },
    input: {
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 14,
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
    },
    notesInput: {
      height: 100,
      paddingTop: 14,
    },
    historySection: {
      marginBottom: 30,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: isDark ? '#fff' : '#000',
      marginBottom: 16,
    },
    historyItem: {
      backgroundColor: isDark ? '#1a1a1a' : '#f8f8f8',
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: 1,
      borderColor: isDark ? '#333' : '#e0e0e0',
    },
    historyType: {
      fontSize: 14,
      fontWeight: '500',
      color: isDark ? '#ccc' : '#666',
      marginBottom: 4,
    },
    historyDescription: {
      fontSize: 16,
      color: isDark ? '#fff' : '#000',
    },
    addHistoryButton: {
      borderWidth: 2,
      borderColor: isDark ? '#333' : '#e0e0e0',
      borderStyle: 'dashed',
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    addHistoryText: {
      fontSize: 16,
      color: isDark ? '#666' : '#999',
      fontWeight: '500',
    },
    createButton: {
      backgroundColor: '#007AFF',
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    createButtonText: {
      color: '#fff',
      fontSize: 18,
      fontWeight: '600',
    },
  });
}