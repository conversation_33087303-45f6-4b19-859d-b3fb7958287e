import { api } from '@/convex/_generated/api';
import { useTheme } from '@/hooks/useTheme';
import { twFullConfig } from '@/utils/twconfig';
import { Feather } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const getInteractionIcon = (type: string) => {
  switch (type) {
    case 'call': return 'phone';
    case 'email': return 'mail';
    case 'meeting': return 'users';
    case 'text': return 'message-square';
    case 'social': return 'share-2';
    default: return 'more-horizontal';
  }
};

export default function ContactDetailsPage() {
  const { theme } = useTheme();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const isDarkMode = theme === 'dark';
  
  const slideAnim = useRef(new Animated.Value(SCREEN_WIDTH)).current;

  const contact = useQuery(api.contacts.getContactById, id ? { id } : 'skip');
  const contactHistory = useQuery(api.contactHistory.getContactHistory, id ? { contactId: id } : 'skip');

  const colors = {
    background: isDarkMode ? (twFullConfig.theme.colors as any).dark : '#FCFCFC',
    pageBackground: isDarkMode ? '#1C1C1E' : '#FFFFFF',
    text: isDarkMode ? '#FFFFFF' : '#000000',
    secondaryText: isDarkMode ? '#A0A0A0' : '#666666',
    border: isDarkMode ? '#3A3A3A' : '#E0E0E0',
    iconBackground: isDarkMode ? '#2A2A2A' : '#F0F0F0',
    buttonBackground: isDarkMode ? '#007AFF' : '#007AFF',
    sectionBackground: isDarkMode ? '#2A2A2A' : '#F8F9FA',
  };

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
      tension: 80,
      friction: 8,
    }).start();
  }, []);


  const handleClose = () => {
    router.back();
  };

  const logNewInteraction = () => {
    router.push(`/(app)/(authenticated)/contacts/${id}/history/new`);
  };

  if (!contact) {
    return (
      <View style={[styles.container, { backgroundColor: colors.pageBackground }]}>
        <Text style={[styles.loadingText, { color: colors.text }]}>Loading contact...</Text>
      </View>
    );
  }

  const animatedStyle = {
    transform: [
      {
        translateX: slideAnim,
      },
    ],
  };

  return (
    <Animated.View
      style={[styles.container, { backgroundColor: colors.pageBackground }, animatedStyle]}
    >
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: colors.border }]}>
        <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
          <Feather name="chevron-left" size={28} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Contact Details</Text>
        <TouchableOpacity 
          onPress={() => router.push(`/(app)/(authenticated)/contacts/${id}/edit`)}
          style={styles.editButton}
        >
          <Feather name="edit-2" size={20} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Profile Section */}
        <View style={[styles.profileSection, { backgroundColor: colors.sectionBackground }]}>
          <View style={styles.avatarContainer}>
            {contact.avatarUrl ? (
              <Image source={{ uri: contact.avatarUrl }} style={styles.avatar} />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: colors.iconBackground }]}>
                <Text style={[styles.avatarText, { color: colors.text }]}>
                  {(contact.name || '?').charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          
          <Text style={[styles.name, { color: colors.text }]}>
            {contact.name || 'Unknown Contact'}
          </Text>
          {contact.jobTitle && (
            <Text style={[styles.jobTitle, { color: colors.secondaryText }]}>
              {contact.jobTitle}
            </Text>
          )}
        </View>

        {/* Contact Information */}
        <View style={styles.infoSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Contact Information</Text>
          
          {contact.company && (
            <TouchableOpacity style={[styles.infoRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="briefcase" size={20} color={colors.text} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.secondaryText }]}>Company</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{contact.company}</Text>
              </View>
            </TouchableOpacity>
          )}

          {contact.email && (
            <TouchableOpacity style={[styles.infoRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="mail" size={20} color={colors.text} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.secondaryText }]}>Email</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{contact.email}</Text>
              </View>
            </TouchableOpacity>
          )}

          {contact.phone && (
            <TouchableOpacity style={[styles.infoRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="phone" size={20} color={colors.text} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.secondaryText }]}>Phone</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{contact.phone}</Text>
              </View>
            </TouchableOpacity>
          )}

          {contact.location && (
            <TouchableOpacity style={[styles.infoRow, { borderBottomColor: colors.border }]}>
              <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
                <Feather name="map-pin" size={20} color={colors.text} />
              </View>
              <View style={styles.infoContent}>
                <Text style={[styles.infoLabel, { color: colors.secondaryText }]}>Location</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{contact.location}</Text>
              </View>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={[styles.infoRow, { borderBottomColor: colors.border }]}>
            <View style={[styles.iconContainer, { backgroundColor: colors.iconBackground }]}>
              <Feather name="clock" size={20} color={colors.text} />
            </View>
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.secondaryText }]}>Last Contact</Text>
              <Text style={[styles.infoValue, { color: colors.text }]}>
                {contact.lastContact ? new Date(contact.lastContact).toLocaleDateString() : 'Never'}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Contact History */}
        <View style={styles.historySection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Recent Interactions</Text>

          {contactHistory && contactHistory.length > 0 ? (
            contactHistory.slice(0, 3).map((history) => (
              <TouchableOpacity
                key={history._id}
                style={[styles.historyItem, { borderBottomColor: colors.border }]}
                onPress={() => router.push(`/(app)/(authenticated)/contacts/${id}/history/${history._id}`)}
              >
                <View style={styles.historyHeader}>
                  <View style={[styles.historyTypeIcon, { backgroundColor: colors.iconBackground }]}>
                    <Feather
                      name={getInteractionIcon(history.interactionType)}
                      size={16}
                      color={colors.text}
                    />
                  </View>
                  <View style={styles.historyContent}>
                    <Text style={[styles.historyTitle, { color: colors.text }]}>{history.title}</Text>
                    <Text style={[styles.historyDate, { color: colors.secondaryText }]}>
                      {new Date(history.dateTime).toLocaleDateString()} • {history.interactionType}
                      {history.duration && ` • ${history.duration} min`}
                    </Text>
                  </View>
                  <Feather name="chevron-right" size={16} color={colors.secondaryText} />
                </View>
                {history.notes && (
                  <Text style={[styles.historyNotes, { color: colors.secondaryText }]} numberOfLines={2}>
                    {history.notes}
                  </Text>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={[styles.emptyHistory, { backgroundColor: colors.sectionBackground }]}>
              <Feather name="clock" size={32} color={colors.secondaryText} />
              <Text style={[styles.emptyHistoryText, { color: colors.secondaryText }]}>
                No interactions recorded yet
              </Text>
            </View>
          )}

          {contactHistory && contactHistory.length > 3 && (
            <TouchableOpacity
              style={styles.viewAllButton}
              onPress={() => router.push(`/(app)/(authenticated)/contacts/${id}/history`)}
            >
              <Text style={[styles.viewAllText, { color: colors.buttonBackground }]}>
                View all {contactHistory.length} interactions
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <TouchableOpacity 
            style={[styles.actionButton, { backgroundColor: colors.buttonBackground }]} 
            onPress={logNewInteraction}
          >
            <Feather name="plus" size={20} color="white" />
            <Text style={styles.actionButtonText}>Log New Interaction</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
  },
  editButton: {
    padding: 8,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  profileSection: {
    paddingVertical: 32,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  jobTitle: {
    fontSize: 16,
    marginBottom: 2,
  },
  company: {
    fontSize: 16,
  },
  infoSection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoContent: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
  },
  actionSection: {
    paddingHorizontal: 16,
    marginTop: 32,
    marginBottom: 32,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
  historySection: {
    paddingHorizontal: 16,
    marginTop: 24,
  },
  historyItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  historyTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  historyDate: {
    fontSize: 14,
  },
  historyNotes: {
    fontSize: 14,
    marginLeft: 44,
    lineHeight: 20,
  },
  emptyHistory: {
    alignItems: 'center',
    paddingVertical: 32,
    borderRadius: 12,
  },
  emptyHistoryText: {
    fontSize: 16,
    marginTop: 8,
  },
  viewAllButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  viewAllText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
