import { ThemeProvider } from '@/hooks/useTheme';
import { Stack } from "expo-router";

const Page = () => {
  return (
    <ThemeProvider>
      <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="login" options={{ headerShown: false }} />
          <Stack.Screen name="verify" />
          <Stack.Screen name="faq" options={({ presentation: 'modal'})}/>
      </Stack>
    </ThemeProvider>
  );
}

export default Page;