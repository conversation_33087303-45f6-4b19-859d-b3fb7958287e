import { useTheme } from '@/hooks/useTheme';
import { emailAtom } from "@/store/login";
import { isClerkAPIResponseError, useSignIn, useSignUp, useSSO } from "@clerk/clerk-expo";
import { Feather } from "@expo/vector-icons";
import Checkbox from "expo-checkbox";
import { useRouter } from "expo-router";
import { useSetAtom } from "jotai";
import { useState } from "react";
import {
    ActivityIndicator,
    KeyboardAvoidingView,
    Linking,
    Platform,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from "react-native";

import { Ionicons } from '@expo/vector-icons';

const Page = () => {
    type LoadingState = 'google' | 'apple' | 'email' | false;
    const [loading, setLoading] = useState<LoadingState>(false);
    const [isTermsChecked, setIsTermsChecked] = useState(false);
    const [email, setEmail] = useState("");
    const setEmailAtom = useSetAtom(emailAtom);

    const { startSSOFlow } = useSSO();
    const { signUp } = useSignUp();
    const { signIn, setActive } = useSignIn();
    const router = useRouter();

    const handleSignInWithSSO = async (strategy: 'oauth_google' | 'oauth_apple') => {
        console.log(`Starting SSO flow with ${strategy}`);
        setLoading(strategy.replace('oauth_', '') as 'google' | 'apple');
    
        try {
            const { createdSessionId } = await startSSOFlow({
                strategy
            });
            
            if (createdSessionId) {
                if (setActive) {
                    await setActive({ session: createdSessionId });
                }
                // The navigation will be handled by the auth state change in the layout
            }
        } catch (err) {
            console.error('SSO Error:', err);
            // Handle error appropriately
        } finally {
            setLoading(false);
        }
    };

    const handleEmailOTP = async () => {
        try {
            setLoading('email');
            setEmailAtom(email);
            await signUp?.create({
                emailAddress: email,
            });
            await signUp?.prepareEmailAddressVerification({strategy: 'email_code'});
            router.push('/verify');
        } catch (error) {
            if (isClerkAPIResponseError(error)) {
                if (error.status === 422) {
                    signInWithEmail();
                }
            }
        } finally {
            setLoading(false);
        }
    };

    const signInWithEmail = async () => {
        try {
            setLoading('email');
            setEmailAtom(email);
            await signIn?.create({
                strategy: 'email_code',
                identifier: email,
            });
            router.push('/verify?isLogin=true');
        } catch (err) {
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    const handleLinkPress = (linkType: 'terms' | 'privacy') => {
        Linking.openURL(linkType === 'terms' ? 'https://example.com/terms' : 'https://example.com/privacy');
    };

    const { theme } = useTheme();
    const isDarkMode = theme === 'dark';

    // Debug logs to check state
    console.log('isTermsChecked:', isTermsChecked);
    console.log('loading:', loading);
    console.log('isDarkMode:', isDarkMode);

    const isLoading = (state: LoadingState, type: 'google' | 'apple' | 'email'): boolean => {
        return state === type;
    };

    return (
        <KeyboardAvoidingView
            className={`flex-1 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
            <View className={`flex-1 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} pt-safe`}>
                <View className='flex-1 p-6'>
                    {/* Header */}
                    <View className='flex-row justify-end mb-8'>
                        <TouchableOpacity onPress={() => router.back()}>
                            <Feather name="x" size={24} color={isDarkMode ? "white" : "black"} />
                        </TouchableOpacity>
                    </View>

                    {/* Title */}
                    <View className='mb-8'>
                        <Text className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-black'}`}>
                            Welcome to AI Caption Editor
                        </Text>
                        <Text className={`text-lg mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Sign in or Create Account to continue
                        </Text>
                    </View>

                    {/* Email Input */}
                    <View className='mb-6'>
                        <Text className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            Email address
                        </Text>
                        <TextInput
                            className={`border rounded-lg px-4 py-3 text-base ${
                                isDarkMode 
                                    ? 'border-gray-600 bg-gray-800 text-white placeholder-gray-400' 
                                    : 'border-gray-300 bg-white text-black placeholder-gray-500'
                            }`}
                            placeholder="Enter your email"
                            value={email}
                            onChangeText={setEmail}
                            keyboardType="email-address"
                            autoCapitalize="none"
                            autoCorrect={false}
                        />
                    </View>

                    {/* Terms and Conditions */}
                    <View className='flex-row items-start mb-6'>
                        <Checkbox
                            value={isTermsChecked}
                            onValueChange={setIsTermsChecked}
                            color={isTermsChecked ? '#3b82f6' : undefined}
                            className='mr-3 mt-1'
                        />
                        <Text className={`text-sm flex-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            I agree to the{' '}
                            <Text 
                                className='text-blue-500 underline'
                                onPress={() => handleLinkPress('terms')}
                            >
                                Terms of Service
                            </Text>{' '}
                            and{' '}
                            <Text 
                                className='text-blue-500 underline'
                                onPress={() => handleLinkPress('privacy')}
                            >
                                Privacy Policy
                            </Text>
                        </Text>
                    </View>

                    {/* Email Sign In Button */}
                    <TouchableOpacity
                        className={`rounded-lg py-4 mb-4 ${
                            !isTermsChecked || !email || loading === 'email'
                                ? 'bg-gray-400'
                                : isDarkMode 
                                    ? 'bg-blue-600' 
                                    : 'bg-blue-500'
                        }`}
                        onPress={handleEmailOTP}
                        disabled={!isTermsChecked || !email || loading === 'email'}
                    >
                        {loading === 'email' ? (
                            <ActivityIndicator color="white" />
                        ) : (
                            <Text className='text-white text-center font-semibold text-base'>
                                Continue with Email
                            </Text>
                        )}
                    </TouchableOpacity>

                    {/* Divider */}
                    <View className='flex-row items-center my-6'>
                        <View className={`flex-1 h-px ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
                        <Text className={`px-4 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            or continue with
                        </Text>
                        <View className={`flex-1 h-px ${isDarkMode ? 'bg-gray-700' : 'bg-gray-300'}`} />
                    </View>

                    {/* Social Login Buttons */}
                    <View style={{ gap: 16 }}>
                        {/* Google Sign In */}
                        <TouchableOpacity
                            style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                justifyContent: 'center',
                                borderRadius: 8,
                                paddingVertical: 16,
                                paddingHorizontal: 16,
                                borderWidth: 1,
                                borderColor: isDarkMode ? '#4B5563' : '#D1D5DB',
                                backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                                minHeight: 50,
                                // Remove opacity manipulation from style
                            }}
                            onPress={() => {
                                console.log('loading:', loading);
                                if (loading !== 'google') {
                                    handleSignInWithSSO('oauth_google');
                                }
                            }}
                            disabled={isLoading(loading, 'google')}
                            activeOpacity={0.7}
                        >
                            {loading === 'google' ? (
                                <ActivityIndicator color={isDarkMode ? "white" : "black"} />
                            ) : (
                                <View style={{ 
                                    flexDirection: 'row', 
                                    alignItems: 'center',
                                    opacity: (isLoading(loading, 'google')) ? 0.5 : 1,
                                }}>
                                    <Ionicons 
                                        name="logo-google" 
                                        size={24} 
                                        color={isDarkMode ? "white" : "black"} 
                                    />
                                    <Text style={{
                                        fontWeight: '600',
                                        marginLeft: 8,
                                        color: isDarkMode ? 'white' : 'black',
                                    }}>
                                        Continue with Google
                                    </Text>
                                </View>
                            )}
                        </TouchableOpacity>

                        {/* Apple Sign In */}
                        {Platform.OS === 'ios' && (
                            <TouchableOpacity
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    borderRadius: 8,
                                    paddingVertical: 16,
                                    paddingHorizontal: 16,
                                    borderWidth: 1,
                                    borderColor: isDarkMode ? '#4B5563' : '#D1D5DB',
                                    backgroundColor: isDarkMode ? '#1F2937' : '#FFFFFF',
                                    minHeight: 50,
                                    // Remove opacity manipulation from style
                                }}
                                onPress={() => {
                                    console.log('Apple button pressed, loading:', loading);
                                    if (loading !== 'apple') {
                                        handleSignInWithSSO('oauth_apple');
                                    }
                                }}
                                disabled={isLoading(loading, 'apple')}
                                activeOpacity={0.7}
                            >
                                {loading === 'apple' ? (
                                    <ActivityIndicator color={isDarkMode ? "white" : "black"} />
                                ) : (
                                    <View style={{ 
                                        flexDirection: 'row', 
                                        alignItems: 'center',
                                        opacity: (isLoading(loading, 'apple')) ? 0.5 : 1,
                                    }}>
                                        <Ionicons 
                                            name="logo-apple" 
                                            size={24} 
                                            color={isDarkMode ? "white" : "black"} 
                                        />
                                        <Text style={{
                                            fontWeight: '600',
                                            marginLeft: 8,
                                            color: isDarkMode ? 'white' : 'black',
                                        }}>
                                            Continue with Apple
                                        </Text>
                                    </View>
                                )}
                            </TouchableOpacity>
                        )}
                    </View>
                </View>
            </View>
        </KeyboardAvoidingView>
    );
};

export default Page;